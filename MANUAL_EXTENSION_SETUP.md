# MTBRMG ERP - Manual VS Code Extension Setup

## 🚀 Quick Setup (Automated)

Since VS Code command line is not available, you can install extensions manually. The project is already configured with all the necessary settings!

## 📦 Essential Extensions to Install

### **Method 1: One-Click Install (Recommended)**
1. Open VS Code
2. Open this project folder
3. VS Code will automatically prompt you to install recommended extensions
4. Click "Install All" when prompted

### **Method 2: Manual Installation**
If the automatic prompt doesn't appear, install these extensions manually:

#### **Frontend Development (Priority: HIGH)**
```
1. Tailwind CSS IntelliSense (bradlc.vscode-tailwindcss)
2. Prettier - Code formatter (esbenp.prettier-vscode)
3. ESLint (dbaeumer.vscode-eslint)
4. TypeScript Importer (pmneo.tsimporter)
5. Auto Rename Tag (formulahendry.auto-rename-tag)
```

#### **Backend Development (Priority: HIGH)**
```
6. Python (ms-python.python)
7. Python Black Formatter (ms-python.black-formatter)
8. Django (batisteo.vscode-django)
9. Pylint (ms-python.pylint)
10. isort (ms-python.isort)
```

#### **Development Workflow (Priority: MEDIUM)**
```
11. GitLens (eamodio.gitlens)
12. REST Client (humao.rest-client)
13. SQL Tools (mtxr.sqltools)
14. PostgreSQL Driver (mtxr.sqltools-driver-pg)
15. Markdown All in One (yzhang.markdown-all-in-one)
```

#### **Nice to Have (Priority: LOW)**
```
16. Material Icon Theme (PKief.material-icon-theme)
17. Git Graph (mhutchie.git-graph)
18. Path Intellisense (christian-kohler.path-intellisense)
19. Thunder Client (rangav.vscode-thunder-client)
20. Project Manager (alefragnani.project-manager)
```

## ✅ What's Already Configured

The setup script has already created:

### **VS Code Configuration Files**
- ✅ `.vscode/settings.json` - Editor settings, formatting, Python path
- ✅ `.vscode/extensions.json` - Recommended extensions list
- ✅ `.vscode/launch.json` - Debugging configuration
- ✅ `.vscode/tasks.json` - Build and run tasks

### **Frontend Configuration**
- ✅ `apps/frontend/.prettierrc` - Code formatting rules
- ✅ `apps/frontend/.eslintrc.json` - Linting rules

### **Backend Configuration**
- ✅ `apps/backend/pyproject.toml` - Python formatting and linting

### **Documentation**
- ✅ `docs/development/README.md` - Development guide
- ✅ `docs/api/README.md` - API documentation template
- ✅ `docs/components/README.md` - Component documentation

## 🚀 How to Use After Setup

### **1. Start Development Servers**
Press `Ctrl+Shift+P` → Type "Tasks: Run Task" → Select:
- "Start Both Servers" (recommended)
- "Start Frontend Dev Server" (Next.js on port 3001)
- "Start Backend Dev Server" (Django on port 8000)

### **2. Debugging**
- Press `F5` to start debugging
- Choose "Next.js: debug server-side" or "Python: Django"

### **3. Code Formatting**
- **Automatic**: Code formats on save
- **Manual**: `Ctrl+Shift+P` → "Format Document"

### **4. Testing APIs**
- Create `.http` files in `docs/api/`
- Use REST Client extension to test endpoints

### **5. Git Integration**
- Enhanced Git features with GitLens
- Visual git graph with Git Graph extension

## 🔧 Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+`` | Toggle terminal |
| `Ctrl+Shift+`` | New terminal |
| `Ctrl+P` | Quick file open |
| `Ctrl+Shift+P` | Command palette |
| `F5` | Start debugging |
| `F12` | Go to definition |
| `Shift+F12` | Find all references |
| `Ctrl+Shift+F` | Search in files |

## 🎯 Benefits You'll Get

### **For AI Assistance (Me!)**
- ✅ Better code analysis and understanding
- ✅ Consistent code formatting for easier reading
- ✅ Type safety with TypeScript
- ✅ Standardized project structure

### **For Your Development**
- ✅ Auto-completion for Tailwind CSS classes
- ✅ Real-time error detection and fixing
- ✅ Automatic code formatting on save
- ✅ Integrated debugging for both frontend and backend
- ✅ Git workflow optimization
- ✅ API testing within VS Code
- ✅ Enhanced responsive design development

## 🚨 Important Notes

1. **Restart VS Code** after installing extensions
2. **Open the project folder** in VS Code (not individual files)
3. **Check the bottom status bar** for Python interpreter and formatting status
4. **Use the integrated terminal** for running commands

## 🔍 Verification

After setup, you should see:
- ✅ Tailwind CSS autocomplete in JSX files
- ✅ Prettier formatting on save
- ✅ Python syntax highlighting and linting
- ✅ Git information in the sidebar
- ✅ Debug configurations available (F5)
- ✅ Tasks available in Command Palette

## 🆘 Troubleshooting

### **Extensions not working?**
1. Restart VS Code
2. Check if extensions are enabled
3. Reload window: `Ctrl+Shift+P` → "Developer: Reload Window"

### **Python not detected?**
1. Check `.vscode/settings.json` has correct Python path
2. Press `Ctrl+Shift+P` → "Python: Select Interpreter"
3. Choose the venv Python: `./apps/backend/venv/bin/python`

### **Tailwind not autocompleting?**
1. Make sure Tailwind CSS extension is installed
2. Check if you're in a `.tsx` or `.jsx` file
3. Restart VS Code

## 🎉 You're All Set!

Your MTBRMG ERP development environment is now optimized for:
- ✅ Responsive design development
- ✅ TypeScript/React development with Next.js
- ✅ Django/Python backend development
- ✅ API testing and debugging
- ✅ Git workflow optimization
- ✅ Code quality and consistency

**Happy coding!** 🚀
