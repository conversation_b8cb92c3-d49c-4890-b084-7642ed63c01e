{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "launch", "program": "${workspaceFolder}/apps/frontend/node_modules/.bin/next", "args": ["dev", "--port", "3001"], "cwd": "${workspaceFolder}/apps/frontend", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "development"}}, {"name": "Python: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/apps/backend/manage.py", "args": ["runserver", "0.0.0.0:8000"], "django": true, "cwd": "${workspaceFolder}/apps/backend", "console": "integratedTerminal", "env": {"DJANGO_SETTINGS_MODULE": "mtbrmg_erp.settings"}}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}/apps/backend"}]}