{"version": "2.0.0", "tasks": [{"label": "Start Frontend Dev Server", "type": "shell", "command": "npm run dev", "group": "build", "options": {"cwd": "${workspaceFolder}/apps/frontend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Backend Dev Server", "type": "shell", "command": "source venv/bin/activate && python manage.py runserver 0.0.0.0:8000", "group": "build", "options": {"cwd": "${workspaceFolder}/apps/backend"}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Both Servers", "dependsOrder": "parallel", "dependsOn": ["Start Frontend Dev Server", "Start Backend Dev Server"], "group": {"kind": "build", "isDefault": true}}, {"label": "Run Frontend Tests", "type": "shell", "command": "npm test", "group": "test", "options": {"cwd": "${workspaceFolder}/apps/frontend"}}, {"label": "Run Backend Tests", "type": "shell", "command": "source venv/bin/activate && python manage.py test", "group": "test", "options": {"cwd": "${workspaceFolder}/apps/backend"}}, {"label": "Format Frontend Code", "type": "shell", "command": "npm run format", "group": "build", "options": {"cwd": "${workspaceFolder}/apps/frontend"}}, {"label": "Format Backend Code", "type": "shell", "command": "source venv/bin/activate && black . && isort .", "group": "build", "options": {"cwd": "${workspaceFolder}/apps/backend"}}]}