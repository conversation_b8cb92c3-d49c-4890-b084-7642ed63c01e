{"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "files.associations": {"*.css": "tailwindcss"}, "python.defaultInterpreterPath": "./apps/backend/venv/bin/python", "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.flake8Enabled": true, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "editor.rulers": [80, 120], "editor.wordWrap": "on", "editor.minimap.enabled": true, "workbench.iconTheme": "material-icon-theme", "git.enableSmartCommit": true, "git.confirmSync": false, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "terminal.integrated.defaultProfile.osx": "bash", "files.exclude": {"**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/venv": true, "**/__pycache__": true, "**/*.pyc": true}}